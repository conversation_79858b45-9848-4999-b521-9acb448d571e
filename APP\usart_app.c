/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/
#include "mcu_cmic_gd32f470vet6.h"
#include "selftest_app.h"
#include "sampling_app.h"
#include "config_app.h"

__IO uint8_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[256] = {0};

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[256];
    va_list arg;
    int len;
    // ????????????��?
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
        usart_data_transmit(usart_periph, buffer[tx_count]);
    }
    
    return len;
}

// 解析时间字符串函数
static int parse_datetime(const char* datetime_str, uint8_t* year, uint8_t* month, uint8_t* day, uint8_t* hour, uint8_t* minute, uint8_t* second)
{
    int full_year, temp_month, temp_day, temp_hour, temp_minute, temp_second;

    // 解析格式: "2025-01-01 12:00:30"
    if(sscanf(datetime_str, "%d-%d-%d %d:%d:%d", &full_year, &temp_month, &temp_day, &temp_hour, &temp_minute, &temp_second) == 6) {
        *year = full_year % 100; // 取年份后两位
        *month = temp_month;
        *day = temp_day;
        *hour = temp_hour;
        *minute = temp_minute;
        *second = temp_second;

        // 简单验证
        if(*month >= 1 && *month <= 12 && *day >= 1 && *day <= 31 &&
           *hour <= 23 && *minute <= 59 && *second <= 59) {
            return 1; // 成功
        }
    }
    return 0; // 失败
}

void uart_task(void)
{
    if(!rx_flag) return;

    // 确保字符串以NULL结尾
    uart_dma_buffer[255] = '\0';

    // 移除字符串末尾的换行符和回车符
    char* cmd = (char*)uart_dma_buffer;
    int len = strlen(cmd);

    // 移除末尾的控制字符
    while(len > 0 && (cmd[len-1] == '\r' || cmd[len-1] == '\n' || cmd[len-1] == ' ' || cmd[len-1] == '\t')) {
        cmd[len-1] = '\0';
        len--;
    }

    // 移除开头的空格
    while(*cmd == ' ' || *cmd == '\t') {
        cmd++;
    }

    // 重新计算长度
    len = strlen(cmd);

    // 如果命令为空，直接返回
    if(len == 0 || *cmd == '\0') {
        rx_flag = 0;
        memset(uart_dma_buffer, 0, 256);
        return;
    }

    // 调试信息：显示处理的命令（可选）
    // my_printf(DEBUG_USART, "DEBUG: Processing command [%s] len=%d\r\n", cmd, len);

    // 首先检查是否在等待配置输入状态
    if(config_state != CONFIG_STATE_NORMAL) {
        config_process_input(cmd);
        rx_flag = 0; // 清除接收标志
        memset(uart_dma_buffer, 0, 256);
        return; // 处理完输入后直接返回
    }

    // 命令处理 - 使用更健壮的字符串比较
    if(strncmp(cmd, "test", 4) == 0 && len == 4) {
        system_selftest();
    }
    else if(strncmp(cmd, "start", 5) == 0 && len == 5) {
        sampling_start();
    }
    else if(strncmp(cmd, "stop", 4) == 0 && len == 4) {
        sampling_stop();
    }
    else if(strncmp(cmd, "conf", 4) == 0 && len == 4) {
        config_cmd_handler();
    }
    else if(strncmp(cmd, "config save", 11) == 0 && len == 11) {
        config_save_handler();
    }
    else if(strncmp(cmd, "config read", 11) == 0 && len == 11) {
        config_read_handler();
    }
    else if(strncmp(cmd, "ratio", 5) == 0 && len == 5) {
        config_ratio_handler();
    }
    else if(strncmp(cmd, "limit", 5) == 0 && len == 5) {
        config_limit_handler();
    }
    else if(strncmp(cmd, "RTC Config", 10) == 0 && len == 10) {
        my_printf(DEBUG_USART, "Input Datetime\r\n");
    }
    else if(strncmp(cmd, "RTC now", 7) == 0 && len == 7) {
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        my_printf(DEBUG_USART, "Current TIME:%s\r\n", time_str);
    }
    else if(strncmp(cmd, "voltage test", 12) == 0 && len == 12) {
        extern uint16_t adc_value[1];
        float real_voltage = adc_to_voltage(adc_value[0]);
        float display_voltage = get_display_voltage(adc_value[0]);
        my_printf(DEBUG_USART, "Voltage Test:\r\n");
        my_printf(DEBUG_USART, "ADC Value: %d\r\n", adc_value[0]);
        my_printf(DEBUG_USART, "Real Voltage: %.3fV\r\n", real_voltage);
        my_printf(DEBUG_USART, "Ratio: %.2f\r\n", config_params.ratio);
        my_printf(DEBUG_USART, "Display Voltage: %.3fV\r\n", display_voltage);
        my_printf(DEBUG_USART, "Limit: %.2fV\r\n", config_params.limit);
        my_printf(DEBUG_USART, "Over Limit: %s\r\n", check_voltage_limit(display_voltage) ? "YES" : "NO");
    }
    else if(strncmp(cmd, "file test", 9) == 0 && len == 9) {
        // 测试文件存储功能
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));

        my_printf(DEBUG_USART, "File Storage Test:\r\n");
        my_printf(DEBUG_USART, "Current time: %s\r\n", time_str);

        // 测试文件夹创建
        sampling_create_folders();

        // 测试正常数据保存
        my_printf(DEBUG_USART, "Testing normal data save...\r\n");
        sampling_save_normal_data(time_str, 1.25f);

        // 测试超限数据保存
        my_printf(DEBUG_USART, "Testing overlimit data save...\r\n");
        sampling_save_overlimit_data(time_str, 2.50f, 2.00f);

        my_printf(DEBUG_USART, "File test completed\r\n");
    }
    else if(strncmp(cmd, "storage init", 12) == 0 && len == 12) {
        // 重新初始化存储系统
        my_printf(DEBUG_USART, "Re-initializing storage system...\r\n");
        sampling_storage_init();
        my_printf(DEBUG_USART, "Storage re-initialization completed\r\n");
    }
    else if(strncmp(cmd, "tf status", 9) == 0 && len == 9) {
        // 检查TF卡状态
        my_printf(DEBUG_USART, "TF Card Status Check:\r\n");

        // 重新初始化SD卡和文件系统
        DSTATUS stat;
        FRESULT result;
        uint16_t retry = 3;

        // 初始化SD卡
        do {
            stat = disk_initialize(0);
            retry--;
        } while((stat != 0) && (retry > 0));

        my_printf(DEBUG_USART, "SD card initialize: %s (code: %d)\r\n",
                 (stat == 0) ? "OK" : "FAILED", stat);

        if(stat == 0) {
            // 检查TF卡容量
            uint32_t capacity_kb = sd_card_capacity_get();
            if(capacity_kb > 0) {
                float capacity_gb = capacity_kb / 1024.0f / 1024.0f;
                my_printf(DEBUG_USART, "TF card capacity: %.2fGB\r\n", capacity_gb);
            } else {
                my_printf(DEBUG_USART, "TF card capacity read failed\r\n");
            }

            // 挂载文件系统
            extern FATFS fs;
            result = f_mount(&fs, "0:", 1); // 立即挂载
            my_printf(DEBUG_USART, "File system mount: %s (code: %d)\r\n",
                     (result == FR_OK) ? "OK" : "FAILED", result);

            if(result == FR_OK) {
                // 测试文件系统
                FIL test_file;
                result = f_open(&test_file, "0:/test.txt", FA_CREATE_ALWAYS | FA_WRITE);
                if(result == FR_OK) {
                    my_printf(DEBUG_USART, "File system: OK\r\n");
                    f_close(&test_file);
                    f_unlink("0:/test.txt"); // 删除测试文件
                } else {
                    my_printf(DEBUG_USART, "File system test failed: %d\r\n", result);
                }
            }
        }
    }
    else if(strncmp(cmd, "hide", 4) == 0 && len == 4) {
        // 启用加密模式
        sampling_set_encrypt_mode(ENCRYPT_MODE_ON);
    }
    else if(strncmp(cmd, "unhide", 6) == 0 && len == 6) {
        // 关闭加密模式
        sampling_set_encrypt_mode(ENCRYPT_MODE_OFF);
    }
    else if(strncmp(cmd, "encrypt test", 12) == 0 && len == 12) {
        // 测试加密功能
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));

        my_printf(DEBUG_USART, "Encryption Test:\r\n");
        my_printf(DEBUG_USART, "Normal format: %s ch0 = 12.50V\r\n", time_str);

        // 测试加密输出
        sampling_output_encrypted(time_str, 12.5f, 0);
        my_printf(DEBUG_USART, "Encrypted format (normal): ");

        sampling_output_encrypted(time_str, 15.8f, 1);
        my_printf(DEBUG_USART, "Encrypted format (overlimit): ");

        my_printf(DEBUG_USART, "Test completed\r\n");
    }
    // 处理时间设置命令（格式：2025-01-01 12:00:30）
    else if(len == 19 && cmd[4] == '-' && cmd[7] == '-' && cmd[10] == ' ' && cmd[13] == ':' && cmd[16] == ':') {
        uint8_t year, month, day, hour, minute, second;
        if(parse_datetime(cmd, &year, &month, &day, &hour, &minute, &second)) {
            rtc_set_time(year, month, day, hour, minute, second);
            my_printf(DEBUG_USART, "RTC Config success\r\nTime:%s\r\n", cmd);
        } else {
            my_printf(DEBUG_USART, "Invalid datetime format\r\n");
        }
    }
    // 其他命令回显（用于调试）
    else {
        my_printf(DEBUG_USART, "Unknown command: [%s] (len=%d)\r\n", cmd, len);
        // 显示每个字符的ASCII码用于调试
        my_printf(DEBUG_USART, "ASCII: ");
        for(int i = 0; i < len && i < 20; i++) {
            my_printf(DEBUG_USART, "%02X ", (uint8_t)cmd[i]);
        }
        my_printf(DEBUG_USART, "\r\n");
    }

    rx_flag = 0; // 清除接收标志
    memset(uart_dma_buffer, 0, 256);
}


